<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>语音识别助手</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
  
  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#1E90FF',
            secondary: '#FF4500',
            neutral: {
              100: '#F5F5F5',
              200: '#E5E5E5',
              300: '#D4D4D4',
              400: '#A3A3A3',
              500: '#737373',
              600: '#525252',
              700: '#404040',
              800: '#262626',
              900: '#171717',
            }
          },
          fontFamily: {
            inter: ['Inter', 'sans-serif'],
          },
          animation: {
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'spin-slow': 'spin 1s linear infinite',
          }
        },
      }
    }
  </script>
  
  <!-- 自定义工具类 -->
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .bg-gradient-primary {
        background: linear-gradient(135deg, #1E90FF 0%, #005FCC 100%);
      }
      .transition-transform-opacity {
        transition-property: transform, opacity;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
      }
    }
  </style>
</head>
<body class="bg-gradient-to-br from-neutral-900 to-neutral-800 min-h-screen font-inter text-white">
  <!-- 顶部导航栏 -->
  <header class="fixed top-0 left-0 right-0 bg-neutral-800/80 backdrop-blur-md z-50 border-b border-neutral-700">
    <div class="container mx-auto px-4 py-3 flex justify-between items-center">
      <h1 class="text-[clamp(1.5rem,3vw,2rem)] font-bold text-white flex items-center">
        <i class="fa-solid fa-wave-square text-primary mr-2"></i>
        语音识别助手
      </h1>
      <div class="flex space-x-4">
        <button id="historyBtn" class="p-2 rounded-full hover:bg-neutral-700 transition-colors" title="历史记录">
          <i class="fa-solid fa-clock text-neutral-300 hover:text-primary"></i>
        </button>
        <button id="settingsBtn" class="p-2 rounded-full hover:bg-neutral-700 transition-colors" title="设置">
          <i class="fa-solid fa-cog text-neutral-300 hover:text-primary"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="container mx-auto px-4 pt-24 pb-16">
    <!-- 状态指示条 -->
    <div class="flex items-center justify-center mb-8">
      <div id="statusIndicator" class="flex items-center bg-neutral-700/50 rounded-full px-4 py-2 backdrop-blur-sm">
        <span id="statusIcon" class="w-3 h-3 rounded-full bg-green-500 mr-2 animate-pulse-slow"></span>
        <span id="statusText" class="text-sm text-neutral-300">在线模式，等待指令...</span>
      </div>
    </div>
    
    <!-- 语音识别主界面 -->
    <div class="max-w-2xl mx-auto">
      <!-- 文件上传按钮 -->
      <div class="flex justify-center mb-4">
        <label class="relative cursor-pointer bg-neutral-700/50 hover:bg-primary/20 backdrop-blur-sm rounded-lg px-4 py-3 transition-all duration-300">
          <span class="text-neutral-300">选择WAV文件</span>
          <input id="fileInput" type="file" accept=".wav" class="hidden">
        </label>
      </div>
      <!-- 麦克风按钮 -->
      <div class="flex justify-center mb-8">
        <button id="recordBtn" class="relative w-[clamp(100px,25vw,160px)] h-[clamp(100px,25vw,160px)] rounded-full bg-gradient-primary flex items-center justify-center shadow-lg shadow-primary/20 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-primary/30">
          <i id="micIcon" class="fa-solid fa-microphone text-white text-[clamp(2rem,5vw,3rem)]"></i>
          <span id="recordingStatus" class="absolute -bottom-8 text-sm font-medium text-white opacity-0 transition-opacity duration-300">点击开始录音</span>
        </button>
      </div>
      
      <!-- 波形可视化 -->
      <div id="waveformContainer" class="h-24 mb-8 flex items-center justify-center overflow-hidden">
        <div id="waveform" class="w-full h-full flex items-center justify-center space-x-1">
          <!-- 波形条会通过JS动态生成 -->
        </div>
      </div>
      
      <!-- 识别结果显示区 -->
      <div class="bg-neutral-800/50 backdrop-blur-md rounded-xl p-5 shadow-xl border border-neutral-700 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-white">识别结果</h2>
          <div class="flex space-x-2">
            <button id="clearBtn" class="text-neutral-400 hover:text-red-400 transition-colors p-1" title="清空">
              <i class="fa-solid fa-trash"></i>
            </button>
            <button id="speakBtn" class="text-neutral-400 hover:text-primary transition-colors p-1" title="语音播报">
              <i class="fa-solid fa-volume-up"></i>
            </button>
          </div>
        </div>
        <div id="resultDisplay" class="min-h-[120px] max-h-[300px] overflow-y-auto pr-2 space-y-3 text-neutral-200">
          <div class="result-item animate-fade-in">
            <div class="flex items-start">
              <span class="text-neutral-500 text-sm mr-2">14:30</span>
              <p>你好，<span class="text-red-400">小明</span>！今天天气晴，气温25℃。</p>
            </div>
          </div>
          <div class="result-item animate-fade-in">
            <div class="flex items-start">
              <span class="text-neutral-500 text-sm mr-2">14:29</span>
              <p>请帮我查询明天的<span class="text-red-400">飞标</span>航班信息。</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 命令快捷按钮 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-8">
        <button class="quick-command bg-neutral-700/50 hover:bg-primary/20 backdrop-blur-sm rounded-lg p-3 transition-all duration-300 flex flex-col items-center">
          <i class="fa-solid fa-music text-primary text-xl mb-1"></i>
          <span class="text-sm text-neutral-300">播放音乐</span>
        </button>
        <button class="quick-command bg-neutral-700/50 hover:bg-primary/20 backdrop-blur-sm rounded-lg p-3 transition-all duration-300 flex flex-col items-center">
          <i class="fa-solid fa-calendar text-primary text-xl mb-1"></i>
          <span class="text-sm text-neutral-300">设置闹钟</span>
        </button>
        <button class="quick-command bg-neutral-700/50 hover:bg-primary/20 backdrop-blur-sm rounded-lg p-3 transition-all duration-300 flex flex-col items-center">
          <i class="fa-solid fa-search text-primary text-xl mb-1"></i>
          <span class="text-sm text-neutral-300">搜索信息</span>
        </button>
        <button class="quick-command bg-neutral-700/50 hover:bg-primary/20 backdrop-blur-sm rounded-lg p-3 transition-all duration-300 flex flex-col items-center">
          <i class="fa-solid fa-plus text-primary text-xl mb-1"></i>
          <span class="text-sm text-neutral-300">添加命令</span>
        </button>
      </div>
    </div>
  </main>

  <!-- 历史记录弹窗 -->
  <div id="historyModal" class="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300">
    <div class="bg-neutral-800 rounded-xl w-full max-w-2xl max-h-[80vh] overflow-hidden shadow-2xl transform scale-95 transition-transform duration-300">
      <div class="p-5 border-b border-neutral-700 flex justify-between items-center">
        <h3 class="text-xl font-bold text-white">识别历史</h3>
        <button id="closeHistoryBtn" class="text-neutral-400 hover:text-white transition-colors">
          <i class="fa-solid fa-times"></i>
        </button>
      </div>
      <div class="p-5 max-h-[calc(80vh-100px)] overflow-y-auto">
        <div class="relative mb-4">
          <input type="text" placeholder="搜索历史记录..." class="w-full bg-neutral-700/50 border border-neutral-600 rounded-lg px-4 py-2 text-white placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary/50">
          <i class="fa-solid fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500"></i>
        </div>
        <div id="historyList" class="space-y-3">
          <div class="bg-neutral-700/30 rounded-lg p-3 hover:bg-neutral-700/50 transition-colors">
            <div class="flex justify-between items-start">
              <div>
                <p class="text-neutral-200">你好，小明！今天天气晴，气温25℃。</p>
                <span class="text-xs text-neutral-500">2025-05-22 14:30:45</span>
              </div>
              <button class="text-neutral-500 hover:text-red-400 transition-colors">
                <i class="fa-solid fa-trash"></i>
              </button>
            </div>
          </div>
          <div class="bg-neutral-700/30 rounded-lg p-3 hover:bg-neutral-700/50 transition-colors">
            <div class="flex justify-between items-start">
              <div>
                <p class="text-neutral-200">请帮我查询明天的飞标航班信息。</p>
                <span class="text-xs text-neutral-500">2025-05-22 14:29:12</span>
              </div>
              <button class="text-neutral-500 hover:text-red-400 transition-colors">
                <i class="fa-solid fa-trash"></i>
              </button>
            </div>
          </div>
          <div class="bg-neutral-700/30 rounded-lg p-3 hover:bg-neutral-700/50 transition-colors">
            <div class="flex justify-between items-start">
              <div>
                <p class="text-neutral-200">打开音乐播放器。</p>
                <span class="text-xs text-neutral-500">2025-05-22 14:27:38</span>
              </div>
              <button class="text-neutral-500 hover:text-red-400 transition-colors">
                <i class="fa-solid fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="p-4 border-t border-neutral-700 flex justify-end">
        <button id="clearAllHistoryBtn" class="text-red-400 hover:text-red-500 transition-colors text-sm font-medium">
          清空全部历史
        </button>
      </div>
    </div>
  </div>

  <!-- 设置弹窗 -->
  <div id="settingsModal" class="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300">
    <div class="bg-neutral-800 rounded-xl w-full max-w-2xl max-h-[80vh] overflow-hidden shadow-2xl transform scale-95 transition-transform duration-300">
      <div class="p-5 border-b border-neutral-700 flex justify-between items-center">
        <h3 class="text-xl font-bold text-white">系统设置</h3>
        <button id="closeSettingsBtn" class="text-neutral-400 hover:text-white transition-colors">
          <i class="fa-solid fa-times"></i>
        </button>
      </div>
      <div class="p-5 max-h-[calc(80vh-100px)] overflow-y-auto">
        <div class="space-y-6">
          <!-- 识别灵敏度 -->
          <div class="setting-item">
            <h4 class="text-lg font-medium text-white mb-3">识别灵敏度</h4>
            <div class="flex items-center space-x-4">
              <i class="fa-solid fa-volume-down text-neutral-400"></i>
              <input type="range" min="0" max="100" value="50" class="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer accent-primary">
              <i class="fa-solid fa-volume-up text-neutral-400"></i>
            </div>
            <div class="flex justify-between text-xs text-neutral-500 mt-1">
              <span>安静环境</span>
              <span>嘈杂环境</span>
            </div>
          </div>
          
          <!-- 唤醒词设置 -->
          <div class="setting-item">
            <div class="flex justify-between items-center mb-3">
              <h4 class="text-lg font-medium text-white">热词唤醒</h4>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" checked class="sr-only peer">
                <div class="w-11 h-6 bg-neutral-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
            <div class="bg-neutral-700/30 rounded-lg p-3">
              <div class="flex items-center mb-2">
                <label class="text-neutral-300 w-24">唤醒词：</label>
                <input type="text" value="小助手" class="flex-1 bg-neutral-800 border border-neutral-600 rounded-lg px-3 py-2 text-white placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary/50">
              </div>
              <button class="text-primary hover:text-primary/80 text-sm flex items-center">
                <i class="fa-solid fa-volume-up mr-1"></i> 试听唤醒效果
              </button>
            </div>
          </div>
          
          <!-- 音频设备 -->
          <div class="setting-item">
            <h4 class="text-lg font-medium text-white mb-3">音频设备</h4>
            <div class="space-y-3">
              <div class="flex items-center">
                <label class="text-neutral-300 w-24">输入设备：</label>
                <select class="flex-1 bg-neutral-800 border border-neutral-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary/50">
                  <option>内置麦克风</option>
                  <option>USB 麦克风</option>
                  <option>蓝牙耳机</option>
                </select>
                <button class="ml-2 p-2 bg-neutral-700 rounded hover:bg-neutral-600 transition-colors">
                  <i class="fa-solid fa-refresh"></i>
                </button>
              </div>
              <div class="flex items-center">
                <label class="text-neutral-300 w-24">输出设备：</label>
                <select class="flex-1 bg-neutral-800 border border-neutral-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary/50">
                  <option>内置扬声器</option>
                  <option>耳机</option>
                  <option>蓝牙音箱</option>
                </select>
                <button class="ml-2 p-2 bg-neutral-700 rounded hover:bg-neutral-600 transition-colors">
                  <i class="fa-solid fa-refresh"></i>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 语言设置 -->
          <div class="setting-item">
            <h4 class="text-lg font-medium text-white mb-3">语言设置</h4>
            <div class="grid grid-cols-3 gap-2">
              <label class="relative">
                <input type="radio" name="language" checked class="sr-only peer">
                <div class="bg-neutral-700/30 rounded-lg p-3 peer-checked:bg-primary/30 peer-checked:border-primary border border-transparent hover:bg-neutral-700/50 transition-colors cursor-pointer">
                  <div class="flex flex-col items-center">
                    <span class="text-lg mb-1">中文</span>
                    <span class="text-xs text-neutral-400">普通话</span>
                  </div>
                </div>
              </label>
              <label class="relative">
                <input type="radio" name="language" class="sr-only peer">
                <div class="bg-neutral-700/30 rounded-lg p-3 peer-checked:bg-primary/30 peer-checked:border-primary border border-transparent hover:bg-neutral-700/50 transition-colors cursor-pointer">
                  <div class="flex flex-col items-center">
                    <span class="text-lg mb-1">English</span>
                    <span class="text-xs text-neutral-400">英语</span>
                  </div>
                </div>
              </label>
              <label class="relative">
                <input type="radio" name="language" class="sr-only peer">
                <div class="bg-neutral-700/30 rounded-lg p-3 peer-checked:bg-primary/30 peer-checked:border-primary border border-transparent hover:bg-neutral-700/50 transition-colors cursor-pointer">
                  <div class="flex flex-col items-center">
                    <span class="text-lg mb-1">粤语</span>
                    <span class="text-xs text-neutral-400">广东话</span>
                  </div>
                </div>
              </label>
            </div>
          </div>
          
          <!-- 离线模式 -->
          <div class="setting-item">
            <div class="flex justify-between items-center">
              <h4 class="text-lg font-medium text-white">离线识别</h4>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" class="sr-only peer">
                <div class="w-11 h-6 bg-neutral-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
            <p class="text-sm text-neutral-400 mt-2">开启后可在无网络环境下识别基础指令（需下载离线模型）</p>
          </div>
        </div>
      </div>
      <div class="p-4 border-t border-neutral-700 flex justify-end space-x-3">
        <button id="cancelSettingsBtn" class="px-4 py-2 bg-neutral-700 rounded-lg hover:bg-neutral-600 transition-colors">
          取消
        </button>
        <button id="saveSettingsBtn" class="px-4 py-2 bg-primary rounded-lg hover:bg-primary/80 transition-colors">
          保存设置
        </button>
      </div>
    </div>
  </div>

  <!-- 底部状态栏 -->
  <footer class="fixed bottom-0 left-0 right-0 bg-neutral-800/80 backdrop-blur-md border-t border-neutral-700 py-2">
    <div class="container mx-auto px-4 flex justify-between items-center text-sm text-neutral-400">
      <div>版本: v1.0.0</div>
      <div>
        <span id="connectionStatus" class="flex items-center">
          <i class="fa-solid fa-wifi mr-1 text-green-500"></i> 在线
        </span>
      </div>
    </div>
  </footer>

  <script>
    // DOM 元素引用
    const recordBtn = document.getElementById('recordBtn');
    const micIcon = document.getElementById('micIcon');
    const recordingStatus = document.getElementById('recordingStatus');
    const waveform = document.getElementById('waveform');
    const resultDisplay = document.getElementById('resultDisplay');
    const clearBtn = document.getElementById('clearBtn');
    const speakBtn = document.getElementById('speakBtn');
    const historyBtn = document.getElementById('historyBtn');
    const historyModal = document.getElementById('historyModal');
    const closeHistoryBtn = document.getElementById('closeHistoryBtn');
    const clearAllHistoryBtn = document.getElementById('clearAllHistoryBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    const settingsModal = document.getElementById('settingsModal');
    const closeSettingsBtn = document.getElementById('closeSettingsBtn');
    const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
    const saveSettingsBtn = document.getElementById('saveSettingsBtn');
    const statusIcon = document.getElementById('statusIcon');
    const statusText = document.getElementById('statusText');
    const connectionStatus = document.getElementById('connectionStatus');
    const fileInput = document.getElementById('fileInput');
    
    // 状态变量
    let isRecording = false;
    let recognition;
    let audioContext;
    let analyser;
    let dataArray;
    let source;
    let animationId;
    
    // 初始化波形可视化
    function initWaveform() {
      // 创建50个波形条
      for (let i = 0; i < 50; i++) {
        const bar = document.createElement('div');
        bar.className = 'wave-bar bg-primary/50 rounded-full w-[3px] transition-all duration-100';
        bar.style.height = '1px';
        bar.style.opacity = '0.5';
        waveform.appendChild(bar);
      }
    }
    
    // 更新波形可视化
    function updateWaveform() {
      if (!analyser) return;
      
      analyser.getByteFrequencyData(dataArray);
      
      const bars = waveform.querySelectorAll('.wave-bar');
      bars.forEach((bar, index) => {
        // 映射频率数据到高度
        const height = dataArray[index] / 255 * 100;
        bar.style.height = `${height}%`;
        
        // 根据高度调整颜色和透明度
        const opacity = Math.min(1, height / 50);
        bar.style.opacity = opacity;
        
        // 根据高度变化添加动画效果
        if (height > 50) {
          bar.classList.add('bg-primary');
        } else {
          bar.classList.remove('bg-primary');
          bar.classList.add('bg-primary/50');
        }
      });
      
      animationId = requestAnimationFrame(updateWaveform);
    }
    
    // 开始录音
    function startRecording() {
      if (isRecording) {
        stopRecording();
      } else {
        // 请求麦克风权限
        navigator.mediaDevices.getUserMedia({ audio: true })
          .then(stream => {
            isRecording = true;
            recordBtn.classList.add('bg-secondary');
            recordBtn.classList.remove('bg-gradient-primary');
            micIcon.classList.remove('fa-microphone');
            micIcon.classList.add('fa-microphone-slash');
            recordingStatus.textContent = '录音中...';
            recordingStatus.classList.remove('opacity-0');
            recordingStatus.classList.add('opacity-100');
            
            // 更新状态指示
            statusIcon.className = 'w-3 h-3 rounded-full bg-red-500 mr-2 animate-pulse-slow';
            statusText.textContent = '正在录音...';
            
            // 初始化音频分析
            if (!audioContext) {
              audioContext = new (window.AudioContext || window.webkitAudioContext)();
              analyser = audioContext.createAnalyser();
              analyser.fftSize = 256;
              const bufferLength = analyser.frequencyBinCount;
              dataArray = new Uint8Array(bufferLength);
              
              source = audioContext.createMediaStreamSource(stream);
              source.connect(analyser);
              
              initWaveform();
              updateWaveform();
            }
            
            // 初始化语音识别
            if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
              recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
              recognition.continuous = true;
              recognition.interimResults = true;
              recognition.lang = 'zh-CN';
              
              recognition.onresult = (event) => {
                const transcript = Array.from(event.results)
                  .map(result => result[0])
                  .map(result => result.transcript)
                  .join('');
                
                // 这里可以添加识别文本的处理逻辑
                updateResultDisplay(transcript);
              };
              
              recognition.onerror = (event) => {
                console.error('语音识别错误:', event.error);
                statusIcon.className = 'w-3 h-3 rounded-full bg-yellow-500 mr-2 animate-pulse-slow';
                statusText.textContent = `识别错误: ${event.error}`;
              };
              
              recognition.onend = () => {
                if (isRecording) {
                  recognition.start();
                }
              };
              
              recognition.start();
            } else {
              statusIcon.className = 'w-3 h-3 rounded-full bg-yellow-500 mr-2 animate-pulse-slow';
              statusText.textContent = '浏览器不支持语音识别';
              alert('您的浏览器不支持语音识别功能，请使用Chrome或Edge浏览器。');
            }
          })
          .catch(error => {
            console.error('获取麦克风权限失败:', error);
            isRecording = false;
            statusIcon.className = 'w-3 h-3 rounded-full bg-red-500 mr-2';
            statusText.textContent = '麦克风权限被拒绝';
            alert('请授予麦克风权限以使用语音识别功能。');
          });
      }
    }
    
    // 停止录音
    function stopRecording() {
      isRecording = false;
      recordBtn.classList.remove('bg-secondary');
      recordBtn.classList.add('bg-gradient-primary');
      micIcon.classList.remove('fa-microphone-slash');
      micIcon.classList.add('fa-microphone');
      recordingStatus.textContent = '点击开始录音';
      recordingStatus.classList.add('opacity-0');
      recordingStatus.classList.remove('opacity-100');
      
      // 更新状态指示
      statusIcon.className = 'w-3 h-3 rounded-full bg-green-500 mr-2 animate-pulse-slow';
      statusText.textContent = '在线模式，等待指令...';
      
      // 停止语音识别
      if (recognition) {
        recognition.stop();
      }
      
      // 停止波形动画
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
      
      // 重置波形条
      const bars = waveform.querySelectorAll('.wave-bar');
      bars.forEach(bar => {
        bar.style.height = '1px';
        bar.style.opacity = '0.5';
        bar.classList.remove('bg-primary');
        bar.classList.add('bg-primary/50');
      });
    }
    
    // 更新识别结果显示
    function updateResultDisplay(text) {
      // 模拟错误识别，实际应用中应根据识别置信度或其他逻辑判断
      let processedText = text;
      if (Math.random() > 0.7) {
        // 随机替换一些词来模拟错误
        const mistakes = [
          { original: '你好', replacement: '泥嚎' },
          { original: '明天', replacement: '冥天' },
          { original: '天气', replacement: '天七' },
          { original: '打开', replacement: '大凯' },
          { original: '关闭', replacement: '关必' }
        ];
        
        const mistake = mistakes[Math.floor(Math.random() * mistakes.length)];
        if (processedText.includes(mistake.original)) {
          processedText = processedText.replace(mistake.original, `<span class="text-red-400">${mistake.replacement}</span>`);
        }
      }
      
      // 创建新的结果项
      const resultItem = document.createElement('div');
      resultItem.className = 'result-item animate-fade-in';
      
      // 获取当前时间
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      const timeString = `${hours}:${minutes}:${seconds}`;
      
      resultItem.innerHTML = `
        <div class="flex items-start">
          <span class="text-neutral-500 text-sm mr-2">${timeString}</span>
          <p>${processedText}</p>
        </div>
      `;
      
      // 添加到结果显示区
      resultDisplay.insertBefore(resultItem, resultDisplay.firstChild);
      
      // 添加淡入动画
      setTimeout(() => {
        resultItem.classList.add('opacity-100');
      }, 10);
    }
    
    // 清空结果显示
    function clearResults() {
      // 添加淡出动画
      const items = resultDisplay.querySelectorAll('.result-item');
      items.forEach(item => {
        item.classList.add('opacity-0');
        item.style.transform = 'translateX(20px)';
      });
      
      // 动画结束后清空
      setTimeout(() => {
        resultDisplay.innerHTML = '';
      }, 300);
    }
    
    // 语音播报结果
    function speakResults() {
      if ('speechSynthesis' in window) {
        const text = Array.from(resultDisplay.querySelectorAll('p'))
          .map(p => p.innerText)
          .join('\n');
        
        if (text) {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.lang = 'zh-CN';
          utterance.rate = 0.9;
          
          // 更改状态指示
          statusIcon.className = 'w-3 h-3 rounded-full bg-blue-500 mr-2 animate-pulse-slow';
          statusText.textContent = '正在语音播报...';
          
          utterance.onend = () => {
            statusIcon.className = 'w-3 h-3 rounded-full bg-green-500 mr-2 animate-pulse-slow';
            statusText.textContent = '在线模式，等待指令...';
          };
          
          window.speechSynthesis.speak(utterance);
        }
      } else {
        alert('您的浏览器不支持语音合成功能。');
      }
    }
    
    // 打开历史记录弹窗
    function openHistoryModal() {
      historyModal.classList.remove('opacity-0', 'pointer-events-none');
      historyModal.querySelector('.bg-neutral-800').classList.remove('scale-95');
      historyModal.querySelector('.bg-neutral-800').classList.add('scale-100');
    }
    
    // 关闭历史记录弹窗
    function closeHistoryModal() {
      historyModal.classList.add('opacity-0', 'pointer-events-none');
      historyModal.querySelector('.bg-neutral-800').classList.remove('scale-100');
      historyModal.querySelector('.bg-neutral-800').classList.add('scale-95');
    }
    
    // 打开设置弹窗
    function openSettingsModal() {
      settingsModal.classList.remove('opacity-0', 'pointer-events-none');
      settingsModal.querySelector('.bg-neutral-800').classList.remove('scale-95');
      settingsModal.querySelector('.bg-neutral-800').classList.add('scale-100');
    }
    
    // 关闭设置弹窗
    function closeSettingsModal() {
      settingsModal.classList.add('opacity-0', 'pointer-events-none');
      settingsModal.querySelector('.bg-neutral-800').classList.remove('scale-100');
      settingsModal.querySelector('.bg-neutral-800').classList.add('scale-95');
    }
    
    // 模拟网络状态变化
    function simulateNetworkChanges() {
      const statuses = [
        { icon: 'fa-wifi', color: 'text-green-500', text: '在线' },
        { icon: 'fa-exclamation-triangle', color: 'text-yellow-500', text: '网络不稳定' },
        { icon: 'fa-wifi-slash', color: 'text-red-500', text: '离线' }
      ];
      
      setInterval(() => {
        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
        connectionStatus.innerHTML = `<i class="fa-solid ${randomStatus.icon} mr-1 ${randomStatus.color}"></i> ${randomStatus.text}`;
        
        // 更新主状态指示器
        if (randomStatus.text === '在线') {
          statusIcon.className = 'w-3 h-3 rounded-full bg-green-500 mr-2 animate-pulse-slow';
          statusText.textContent = '在线模式，等待指令...';
        } else if (randomStatus.text === '网络不稳定') {
          statusIcon.className = 'w-3 h-3 rounded-full bg-yellow-500 mr-2 animate-pulse-slow';
          statusText.textContent = '网络不稳定，可能影响识别';
        } else {
          statusIcon.className = 'w-3 h-3 rounded-full bg-red-500 mr-2';
          statusText.textContent = '离线模式，仅支持基础指令';
        }
      }, 10000);
    }
    
    // 绑定事件监听
    function bindEvents() {
      recordBtn.addEventListener('click', startRecording);
      clearBtn.addEventListener('click', clearResults);
      speakBtn.addEventListener('click', speakResults);
      historyBtn.addEventListener('click', openHistoryModal);
      closeHistoryBtn.addEventListener('click', closeHistoryModal);
      clearAllHistoryBtn.addEventListener('click', () => {
        if (confirm('确定要清空所有历史记录吗？')) {
          const historyItems = document.querySelectorAll('#historyList > div');
          historyItems.forEach(item => {
            item.classList.add('opacity-0');
            item.style.transform = 'translateX(20px)';
          });
          
          setTimeout(() => {
            document.getElementById('historyList').innerHTML = '';
          }, 300);
        }
      });
      
      settingsBtn.addEventListener('click', openSettingsModal);
      closeSettingsBtn.addEventListener('click', closeSettingsModal);
      cancelSettingsBtn.addEventListener('click', closeSettingsModal);
      saveSettingsBtn.addEventListener('click', () => {
        // 模拟保存设置
        statusIcon.className = 'w-3 h-3 rounded-full bg-green-500 mr-2 animate-pulse-slow';
        statusText.textContent = '设置已保存';
        
        setTimeout(() => {
          statusText.textContent = '在线模式，等待指令...';
        }, 2000);
        
        closeSettingsModal();
      });
      
      // 点击弹窗外部关闭弹窗
      historyModal.addEventListener('click', (e) => {
        if (e.target === historyModal) {
          closeHistoryModal();
        }
      });
      
      settingsModal.addEventListener('click', (e) => {
        if (e.target === settingsModal) {
          closeSettingsModal();
        }
      });
      
      // 快捷命令按钮事件
      document.querySelectorAll('.quick-command').forEach(btn => {
        btn.addEventListener('click', () => {
          const command = btn.querySelector('span').textContent;
          updateResultDisplay(`<span class="text-primary">执行命令:</span> ${command}`);
          
          // 模拟命令执行效果
          recordBtn.classList.add('animate-pulse');
          setTimeout(() => {
            recordBtn.classList.remove('animate-pulse');
          }, 1000);
        });
      });
    }
    
    // 上传文件处理
    function handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      // 更新状态
      statusIcon.className = 'w-3 h-3 rounded-full bg-blue-500 mr-2 animate-pulse-slow';
      statusText.textContent = '正在识别文件...';
      
      const formData = new FormData();
      formData.append('audio', file);
      
      // 调用Python后端API进行语音识别
      fetch('http://localhost:5000/recognize', {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          throw new Error(data.error);
        }
        // 更新识别结果到界面
        updateResultDisplay(data.text);
        
        // 更新状态为成功
        statusIcon.className = 'w-3 h-3 rounded-full bg-green-500 mr-2 animate-pulse-slow';
        statusText.textContent = '识别完成';
      })
      .catch(error => {
        console.error('识别错误:', error);
        statusIcon.className = 'w-3 h-3 rounded-full bg-red-500 mr-2';
        statusText.textContent = '识别失败: ' + error.message;
      })
      .finally(() => {
        // 重置文件输入
        event.target.value = '';
      });
    }

    // 初始化
    function init() {
      bindEvents();
      simulateNetworkChanges();
      
      // 添加文件上传事件监听
      fileInput.addEventListener('change', handleFileUpload);
    }
    
    // 页面加载完成后初始化
    window.addEventListener('DOMContentLoaded', init);
  </script>