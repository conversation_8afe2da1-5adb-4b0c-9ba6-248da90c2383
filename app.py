from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import scipy.io.wavfile as wf
import python_speech_features as sf
import numpy as np
import pickle
from werkzeug.utils import secure_filename

app = Flask(__name__)
CORS(app)  # 启用CORS支持


# 加载训练好的模型
def load_models():
    try:
        with open('speech_models.pkl', 'rb') as f:
            return pickle.load(f)
    except FileNotFoundError:
        return None


# 使用模型进行预测
def predict(audio_path, models):
    if not models:
        return "错误：模型未加载"

    try:
        sample_rate, sigs = wf.read(audio_path)
        mfcc = sf.mfcc(sigs, sample_rate, nfft=1200)

        scores = []
        for label, model in models.items():
            score = model.score(mfcc)
            scores.append((score, label))

        # 返回得分最高的标签
        best_score, best_label = max(scores)
        return best_label
    except Exception as e:
        return f"识别错误：{str(e)}"


# 新增：根路径处理
@app.route('/')
def index():
    return "语音识别服务已启动", 200


@app.route('/recognize', methods=['POST'])
def recognize_speech():
    if 'audio' not in request.files:
        return jsonify({'error': '没有上传文件'}), 400

    file = request.files['audio']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400

    if not file.filename.endswith('.wav'):
        return jsonify({'error': '请上传WAV格式的文件'}), 400

    try:
        # 保存上传的文件
        filename = secure_filename(file.filename)
        temp_path = os.path.join('temp', filename)
        os.makedirs('temp', exist_ok=True)
        file.save(temp_path)

        # 加载模型并进行预测
        models = load_models()
        if not models:
            return jsonify({'error': '模型未找到，请先训练模型'}), 500

        result = predict(temp_path, models)

        # 删除临时文件
        os.remove(temp_path)

        return jsonify({'text': result})

    except Exception as e:
        return jsonify({'error': str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True)