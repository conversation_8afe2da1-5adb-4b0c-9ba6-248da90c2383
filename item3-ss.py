# 项目实施代码
import warnings
import os
import logging
import numpy as np
import scipy.io.wavfile as wf
import python_speech_features as sf
import hmmlearn.hmm as hl
import pickle
import sys

# 设置系统编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

os.environ["OMP_NUM_THREADS"]='1'
warnings.filterwarnings('ignore')
np.seterr(all='ignore')
logging.getLogger("hmmlearn").setLevel("CRITICAL")

# 定义语音文件的路径和标签的映射字典函数
def search_speeches(directory, speeches):
    try:
        directory = os.path.abspath(directory)  # 转换为绝对路径
        if not os.path.isdir(directory):
            raise IOError(f"路径 {directory} 不存在")
        
        print(f"正在搜索目录: {directory}")
        for entry in os.listdir(directory):
            label = os.path.basename(directory)  # 使用目录名作为标签
            path = os.path.join(directory, entry)
            
            try:
                if os.path.isdir(path):
                    search_speeches(path, speeches)
                elif os.path.isfile(path) and path.endswith('.wav'):
                    if label not in speeches:
                        speeches[label] = []
                    speeches[label].append(path)
                    print(f"找到音频文件: {path}, 标签: {label}")
            except Exception as e:
                print(f"处理路径时出错 {path}: {str(e)}")
                continue
    except Exception as e:
        print(f"访问目录时出错 {directory}: {str(e)}")
        raise

def extract_mfcc(wav_file):
    """从WAV文件提取MFCC特征"""
    try:
        sample_rate, sigs = wf.read(wav_file)
        mfcc = sf.mfcc(sigs, sample_rate, nfft=1200)
        return mfcc
    except Exception as e:
        print(f"处理文件 {wav_file} 时出错: {str(e)}")
        return None

def train_models():
    try:
        # 获取当前脚本的绝对路径
        script_path = os.path.abspath(__file__)
        current_dir = os.path.dirname(script_path)
        print(f"当前脚本路径: {script_path}")
        print(f"当前工作目录: {current_dir}")
        
        # 构建训练数据目录的完整路径
        train_data_dir = os.path.join(current_dir, "speeches", "train")
        train_data_dir = os.path.abspath(train_data_dir)  # 转换为绝对路径
        
        print(f"开始搜索训练数据，目录: {train_data_dir}")
        
        # 收集训练数据
        train_speeches = {}
        try:
            search_speeches(train_data_dir, train_speeches)
        except IOError as e:
            print(f"错误: {str(e)}")
            print("请确保在正确的目录下运行脚本，并且训练数据目录结构正确")
            return None
        
        if not train_speeches:
            print("没有找到训练数据！请检查数据目录结构是否正确")
            return None
        
        print("\n开始训练模型...")
        print(f"找到的语音类别: {list(train_speeches.keys())}")
        
        models = {}
        for label, filenames in train_speeches.items():
            print(f"\n处理类别 '{label}'...")
            mfccs = []
            
            # 提取所有音频文件的MFCC特征
            for filename in filenames:
                print(f"处理文件: {filename}")
                mfcc = extract_mfcc(filename)
                if mfcc is not None:
                    mfccs.append(mfcc)
            
            if not mfccs:
                print(f"警告: 类别 '{label}' 没有有效的MFCC特征，跳过")
                continue
            
            # 将所有MFCC特征连接成一个数组
            X = np.vstack(mfccs)
            
            # 训练HMM模型
            print(f"训练类别 '{label}' 的HMM模型...")
            try:
                model = hl.GMMHMM(
                    n_components=4,  # HMM状态数
                    n_mix=1,        # 每个状态的高斯混合数
                    covariance_type='diag',
                    random_state=42,
                    n_iter=100,
                    verbose=True
                )
                model.fit(X)
                models[label] = model
                print(f"类别 '{label}' 的模型训练完成")
            except Exception as e:
                print(f"训练类别 '{label}' 的模型时出错: {str(e)}")
                continue
        
        if not models:
            print("错误：没有成功训练任何模型！")
            return None
        
        # 保存训练好的模型
        try:
            model_path = os.path.join(current_dir, 'speech_models.pkl')
            with open(model_path, 'wb') as f:
                pickle.dump(models, f)
            print(f"\n模型已成功保存到: {model_path}")
            return models
        except Exception as e:
            print(f"保存模型时出错: {str(e)}")
            return None
            
    except Exception as e:
        print(f"训练过程中出现错误: {str(e)}")
        return None

if __name__ == '__main__':
    try:
        print("开始语音识别模型训练程序...")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"Python版本: {sys.version}")
        print(f"系统平台: {sys.platform}")
        
        models = train_models()
        if models:
            print("\n训练成功完成！")
            print(f"共训练了 {len(models)} 个类别的模型")
            print("类别列表:", list(models.keys()))
        else:
            print("\n训练失败！请检查错误信息并修复问题")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
